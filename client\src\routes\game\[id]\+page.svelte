<script lang="ts">
  /**
   * Game Page Component
   *
   * This page accepts a "token" URL parameter containing a JWT with game session data:
   * - gameId: The game identifier
   * - roomId: Multiplayer room identifier
   * - scoreSubmitId: Unique ID for score submission tracking
   * - authToken: Authentication token for the game server
   *
   * Example URL: /game/[gameId]?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
   */
  import { page } from "$app/state";

  import * as utilsModule from "$lib/utils";
  import * as socketModule from "$lib/socket";
  import * as gamesModule from "$lib/games";

  import Preloading from "$lib/components/Preloading.svelte";
  import StartScreen from "$lib/components/StartScreen.svelte";
  import GameHUD from "$lib/components/GameHUD.svelte";
  import Countdown from "$lib/components/Countdown.svelte";
  import EndGame from "$lib/components/EndGame.svelte";
  import ErrorModal from "$lib/components/ErrorModal.svelte";
  import { gameState, gameActions } from "$lib/stores";

  import type { SocketClient } from "$lib/socket";

  // JWT token payload interface
  interface TokenPayload {
    gameId: string;
    roomId: string;
    scoreSubmitId: string;
    authToken: string;
    exp?: number;
    iat?: number;
  }

  let gameInstance: any = $state(null);
  let gameContainer: HTMLDivElement | undefined = $state();
  let showCountdown = $state(false);
  let showEndGame = $state(false);
  let postMessageHandler: any = $state(null);
  let socketClient: SocketClient | null = $state(null);
  let createGame: any = $state(null);
  let tokenData: TokenPayload | null = $state(null);

  // Error modal state
  let showErrorModal = $state(false);
  let errorMessage = $state("");
  let errorType = $state("");

  const gameId = $derived(page.params.id);
  let token = $derived(page.url.searchParams.get("token"));

  // Function to decode JWT token (simple base64 decode for payload)
  function decodeJWT(token: string): TokenPayload | null {
    try {
      const parts = token.split(".");
      if (parts.length !== 3) {
        console.error("Invalid JWT format");
        return null;
      }

      // Decode the payload (second part)
      const payload = parts[1];
      // Add padding if needed
      const paddedPayload =
        payload + "=".repeat((4 - (payload.length % 4)) % 4);
      const decodedPayload = atob(
        paddedPayload.replace(/-/g, "+").replace(/_/g, "/")
      );

      return JSON.parse(decodedPayload) as TokenPayload;
    } catch (error) {
      console.error("Failed to decode JWT token:", error);
      return null;
    }
  }

  $effect(() => {
    console.log("Game ID:", gameId);
    console.log("Token:", token);

    // Decode JWT token if provided
    if (token) {
      tokenData = decodeJWT(token);
      token = null;
      if (tokenData) {
        console.log("Decoded token data:", {
          gameId: tokenData.gameId,
          roomId: tokenData.roomId,
          scoreSubmitId: tokenData.scoreSubmitId,
          hasAuthToken: !!tokenData.authToken,
        });

        // Validate that the game ID from token matches the URL parameter
        if (tokenData.gameId !== gameId) {
          console.warn(
            `Game ID mismatch: URL has '${gameId}', token has '${tokenData.gameId}'`
          );
        }

        // Update game state with token data
        gameActions.setRoomData(
          tokenData.roomId,
          tokenData.authToken,
          tokenData.scoreSubmitId
        );
        gameActions.setGameId(tokenData.gameId);
      } else {
        console.error(
          "Failed to decode token - invalid JWT format or corrupted data"
        );
      }
    } else {
      console.log("No token provided - running in development mode");
    }

    postMessageHandler = utilsModule.postMessageHandler;

    // Handle async operations in a separate function
    const initializeGame = async () => {
      try {
        socketClient = socketModule.socketClient;

        if (!socketClient) {
          console.error("Socket client not available");
          return;
        }

        // Use auth token from JWT if available, otherwise fallback to test token
        const authToken = tokenData?.authToken || "test-token";

        await socketClient.connect(authToken, {
          onScoreUpdate: (score: number) => {
            gameActions.updateScore(score);
          },
          onGameComplete: (_finalScore: number) => {
            gameActions.endGame();
            showEndGame = true;
          },
        });

        createGame = gamesModule.createGame(
          gameId,
          "game-container",
          socketClient
        );

        await createGame.init();
      } catch (error) {
        console.error("Failed to initialize game:", error);
      }
    };

    // Call the async function
    initializeGame();

    // Cleanup function (equivalent to onDestroy)
    return () => {
      if (gameInstance) {
        gameInstance.destroy();
      }
      if (socketClient) {
        socketClient.disconnect();
      }
      if (createGame) {
        createGame.destroy();
      }
      // gameActions.resetGame();
    };
  });

  function handleStartGameButton() {
    console.log("Countdown complete");

    showCountdown = true;
    gameActions.initGame();
    createGame.start();
  }

  // Error modal functions
  function showError(message: string, type: string = "error") {
    errorMessage = message;
    errorType = type;
    showErrorModal = true;

    // Stop the game instance
    if (gameInstance) {
      gameInstance.scene.pause();
    }
  }

  function handleErrorClose() {
    // TODO: Post message trigeer back button
  }

  // Make error function globally available for Phaser scenes
  if (typeof window !== "undefined") {
    (window as any).showGameError = showError;
  }
</script>

<svelte:head>
  <title>TicTaps - {gameId}</title>
</svelte:head>

<Preloading progress={$gameState.loadingProgress} />

{#if !$gameState.isCountdown && !$gameState.isLoading}
  <StartScreen handleStartClick={handleStartGameButton} {gameId} />
{/if}

<div class="w-screen h-screen overflow-hidden relative">
  {#if $gameState.isPlaying}
    <GameHUD
      score={$gameState.score}
      time={$gameState.time}
      totalTime={$gameState.totalTime}
      lives={$gameState.lives}
      maxLives={$gameState.maxLives}
      opponentScore={$gameState.opponentScore}
      showOpponent={$gameState.opponentScore !== null}
    />
  {/if}

  <div
    class="w-full h-full box-border"
    bind:this={gameContainer}
    id="game-container"
  >
    <!-- games will be mounted here -->
  </div>

  <Countdown show={showCountdown} duration={3} />

  <EndGame show={$gameState.gameOver} finalScore={$gameState.score} />

  <ErrorModal
    isVisible={showErrorModal}
    {errorMessage}
    {errorType}
    onClose={handleErrorClose}
  />
</div>
