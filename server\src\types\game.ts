export type GameType = 'finger-frenzy' | 'bingo' | 'matching-mayhem' | 'numbers';

export interface GameAction {
  type: string;
  data: Record<string, unknown>;
  timestamp: number;
}

export interface GameScore {
  playerId: string;
  score: number;
  gameType: GameType;
  timestamp: number;
}

export interface GameState {
  gameType: GameType;
  status: 'waiting' | 'starting' | 'active' | 'paused' | 'ended';
  startTime?: number;
  score: number;
  scoreAry: number[];
  lives: number;
}

export interface GameResults {
  gameType: GameType;
  score:  number;
  endReason: EndReason;
}

/**
 * Game initialization result
 */
export interface GameInitResult {
  success: boolean;
  gameState?: GameState;
  message?: string;
}

/**
 * Reasons for ending a game
 */
export type EndReason = 'completed'| 'no_lives' | 'timeout' | 'disconnection' | 'manual';
// 'timeout' | 'no_lives' | 'manual';

/**
 * Socket event data structures
 */
export interface GameStartData {
  roomId: string;
  gameId: string;
  submitScoreId?: string;
}

export interface GameEndData {
  roomId: string;
  gameId: string;
  submitScoreId?: string;
  reason?: EndReason;
}

export interface GameActionData {
  roomId: string;
  gameId: string;
  action: {
    type: string;
    data: Record<string, unknown>;
  };
}

/**
 * Base interface for game action results
 * All game-specific action results should extend this interface
 */
export interface BaseActionResult {
  success: boolean;
  isCorrect: boolean;
  points: number;
  newScore: number;
  newLives: number;
  gameEnded: boolean;
}