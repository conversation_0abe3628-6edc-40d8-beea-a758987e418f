import type {
  BaseGameData,
  GameConfig,
  BaseGameActionData
} from './BaseGameController';
import type {
  GridBlock,
  TileTapResult,
  GridState,
  TileStates
} from '../../../types/fingerFrenzy';
import type { GameType } from '../../../types/game';

/**
 * FingerFrenzy-specific game configuration
 */
export interface FingerFrenzyConfig extends GameConfig {
  gameType: 'finger-frenzy';
  maxLives: number;
  gridSize: number;
  initialActiveBlocks: number;
  wrongClickPenalty: number;
  scoreTiers: {
    fast: number;
    mediumFast: number;
    medium: number;
    mediumSlow: number;
    slow: number;
  };
  scoreTierThresholds: {
    fast: number;
    mediumFast: number;
    medium: number;
    mediumSlow: number;
  };
}

/**
 * FingerFrenzy-specific game data
 */
export interface FingerFrenzyGameData extends BaseGameData {
  grid: GridBlock[];
  activeBlockCount: number;
}

/**
 * FingerFrenzy-specific action result
 */
export interface FingerFrenzyActionResult extends TileTapResult {
  // TileTapResult already extends BaseActionResult
  newBlock?: GridBlock | null;
  message?: string;
}

/**
 * FingerFrenzy tile tap action data
 */
export interface FingerFrenzyTileTapActionData extends BaseGameActionData {
  action: {
    type: 'tile_tap';
    data: {
      tileId: string;
      reactionTime: number;
      clickTime?: number;
    };
  };
}

/**
 * FingerFrenzy game state data for client communication
 */
export interface FingerFrenzyGameStateData {
  gridState: GridState | null;
  tileStates?: TileStates | null;
}

/**
 * Initial position interface for block activation
 */
export interface InitialPosition {
  row: number;
  col: number;
}
