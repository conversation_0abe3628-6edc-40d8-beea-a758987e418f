import { Server, Socket } from 'socket.io';
import { GameService } from '../../services/gameService';
import { logger } from '../../utils/logger';
import { GAME_TYPES } from '../../utils/constants';
import { GameState, GameAction } from '../../types/game';
import { 
  NumberSequenceGameState,
  SequenceSelectActionData,
  NumberSequenceGameStartedData,
  RoundData 
} from '../../types/numberSequence';

export default class NumberSequenceController {
  private gameService: GameService;
  private gameStates: Map<string, NumberSequenceGameState> = new Map();
  private socketMap: Map<string, Socket> = new Map();

  constructor(gameService: GameService) {
    this.gameService = gameService;
  }

  /**
   * Initialize and start a new Number Sequence game session
   */
  private initializeAndStartGame(roomId: string, socket: Socket) {
    const started = this.gameService.startGame(roomId, socket);
    if (!started) {
      return { success: false, message: 'Failed to start game' };
    }

    // Initialize Number Sequence specific game state
    const initialState: NumberSequenceGameState = {
      gameType: 'numbers',
      status: 'active',
      score: 0,
      scoreAry: [],
      lives: 3,
      currentSequence: this.generateSequence(1),
      currentIndex: 0,
      streak: 0,
      currentLevel: 1,
      isRoundActive: true,
      roundStartTime: Date.now(),
      roundsCompleted: 0
    };

    this.gameStates.set(roomId, initialState);
    this.socketMap.set(roomId, socket);

    return { success: true, gameState: initialState };
  }

  /**
   * Generate a sequence for the given level
   */
  private generateSequence(level: number): number[] {
    const length = Math.min(3 + level, 10); // Increase sequence length with level, max 10
    const sequence: number[] = [];
    
    for (let i = 0; i < length; i++) {
      sequence.push(Math.floor(Math.random() * 9) + 1); // Numbers 1-9
    }
    
    return sequence;
  }

  /**
   * Handle game start event
   */
  private handleGameStart(socket: Socket, data: any): void {
    const { roomId, gameId } = data;

    if (!roomId || !gameId) {
      socket.emit('error', {
        message: 'Missing required data for game start'
      });
      return;
    }

    try {
      const result = this.initializeAndStartGame(roomId, socket);

      if (result.success && result.gameState) {
        const gameStartedData: NumberSequenceGameStartedData = {
          gameState: {
            score: result.gameState.score,
            lives: result.gameState.lives,
            isActive: result.gameState.status === 'active',
            startTime: result.gameState.startTime || Date.now()
          },
          currentSequence: result.gameState.currentSequence,
          currentLevel: result.gameState.currentLevel,
          message: 'Game started!'
        };

        socket.emit('started', gameStartedData);
        logger.info(`${gameId} game started in room ${roomId}`);
      } else {
        socket.emit('error', {
          message: result.message || 'Failed to start game'
        });
      }
    } catch (error) {
      logger.error(`Error starting ${gameId} game in room ${roomId}:`, error);
      socket.emit('error', {
        message: 'Internal server error'
      });
    }
  }

  /**
   * Handle number selection action
   */
  private handleSequenceSelectAction(socket: Socket, data: SequenceSelectActionData): void {
    const { roomId } = data;
    const { selectedNumber } = data.action.data;

    if (!roomId) {
      socket.emit('error', {
        message: 'Missing roomId for sequence select action'
      });
      return;
    }

    try {
      const gameState = this.gameStates.get(roomId);
      if (!gameState) {
        socket.emit('error', {
          message: 'No game state found for this room'
        });
        return;
      }

      const isCorrect = selectedNumber === gameState.currentSequence[gameState.currentIndex];
      
      if (isCorrect) {
        gameState.currentIndex++;
        gameState.score += 10 * gameState.currentLevel;
        gameState.scoreAry.push(gameState.score);

        // Check if sequence is completed
        if (gameState.currentIndex >= gameState.currentSequence.length) {
          // Level complete
          gameState.currentLevel++;
          gameState.currentSequence = this.generateSequence(gameState.currentLevel);
          gameState.currentIndex = 0;
          gameState.roundsCompleted++;
          gameState.streak++;
        }

        socket.emit('action_result', {
          success: true,
          isCorrect: true,
          newScore: gameState.score,
          currentIndex: gameState.currentIndex,
          nextSequence: gameState.currentIndex === 0 ? gameState.currentSequence : undefined,
          currentLevel: gameState.currentLevel
        });
      } else {
        // Wrong number selected
        gameState.lives--;
        gameState.streak = 0;

        if (gameState.lives <= 0) {
          // Game over
          const results = this.gameService.endGame(roomId, 'no_lives');
          if (results) {
            socket.emit('ended', results);
          }
          return;
        }

        // Reset current sequence
        gameState.currentIndex = 0;
        socket.emit('action_result', {
          success: true,
          isCorrect: false,
          newScore: gameState.score,
          currentIndex: gameState.currentIndex,
          livesRemaining: gameState.lives
        });
      }

      this.gameStates.set(roomId, gameState);
    } catch (error) {
      logger.error(`Error processing sequence select action in room ${roomId}:`, error);
      socket.emit('error', {
        message: 'Internal server error'
      });
    }
  }

  /**
   * Handle game action
   */
  private handleGameAction(socket: Socket, data: any): void {
    const { roomId, gameId, action } = data;

    if (!roomId || !gameId || !action) {
      socket.emit('error', {
        message: 'Missing required data for game action'
      });
      return;
    }

    try {
      switch (action.type) {
        case 'sequence_select':
          this.handleSequenceSelectAction(socket, data as SequenceSelectActionData);
          break;
        default:
          socket.emit('error', {
            message: `Unknown action type: ${action.type}`
          });
      }
    } catch (error) {
      logger.error(`Error processing game action in room ${roomId}:`, error);
      socket.emit('error', {
        message: 'Internal server error'
      });
    }
  }

  /**
   * Handle game end
   */
  private handleGameEnd(socket: Socket, data: any): void {
    const { roomId } = data;

    if (!roomId) {
      socket.emit('error', {
        message: 'Missing roomId for game end'
      });
      return;
    }

    try {
      const results = this.gameService.endGame(roomId, 'manual');
      if (results) {
        socket.emit('ended', results);
      }
      
      // Cleanup
      this.cleanup(roomId);
    } catch (error) {
      logger.error(`Error ending game in room ${roomId}:`, error);
      socket.emit('error', {
        message: 'Internal server error'
      });
    }
  }

  /**
   * Setup socket event handlers for Number Sequence
   */
  public setupSocketHandlers(_io: Server, socket: Socket): void {
    // Generic game start event
    socket.on('start', (data) => {
      if (data.gameId === GAME_TYPES.NUMBER_SEQUENCE) {
        this.handleGameStart(socket, data);
      }
    });

    // Generic game end event
    socket.on('end', (data) => {
      if (data.gameId === GAME_TYPES.NUMBER_SEQUENCE) {
        this.handleGameEnd(socket, data);
      }
    });

    // Generic game action event (for game-specific actions)
    socket.on('action', (data) => {
      if (data.gameId === GAME_TYPES.NUMBER_SEQUENCE) {
        this.handleGameAction(socket, data);
      }
    });
  }

  /**
   * Clean up game resources
   */
  public cleanup(roomId: string): void {
    this.gameStates.delete(roomId);
    this.socketMap.delete(roomId);
  }
}
