import * as Phaser from 'phaser';
import BingoCell from '../objects/BingoCell';
import RightNumber from '../objects/RightNumber';

import ScoreManager from '../../managers/ScoreManager';
// import TimerManager from '../../managers/TimerManager';
import LivesManager from '../../managers/LivesManager';
// import TimerBarUI from '../ui/TimerBarUI';

import type {
  BingoCellData,
  WinResult,
} from '../types/BingoTypes';
import {
  BINGO_CONSTANTS,
  PATTERN_DISPLAY_NAMES
} from '../config/GameConfig';
import type { SocketClient } from '$lib/socket';
import type { BingoConfig } from '../index';
import { gameActions } from '$lib/stores';
// gameActions removed - using local managers for UI updates

/**
 * Equivalent to GameController in Unity
 */
export default class GameScene extends Phaser.Scene {
  private static readonly BINGO_COLUMNS = BINGO_CONSTANTS.COLUMNS;

  // Game state properties
  public gameEnd!: boolean;

  private UIContainer!: Phaser.GameObjects.Container;

  // Server communication (following FingerFrenzy pattern)
  private socketClient: SocketClient | null = null;
  private gameId: string = 'bingo';
  private roomId: string = 'room-' + Date.now().toString(36);

  // UI-only properties (server manages game state)
  private bingoCard!: BingoCellData[][];

  // Game objects (UI only)
  private bgoCells!: BingoCell[];
  private rightNumbers!: RightNumber[];
  private rightPositions!: { x: number; y: number }[];

  // UI elements
  private countdownPanel!: Phaser.GameObjects.Container;
  private countdownText!: Phaser.GameObjects.Image;

  private scoreManager!: ScoreManager;
  // private timerManager!: TimerManager;
  private livesManager!: LivesManager;
  // private timerBarUI!: TimerBarUI;

  // Add camera dimensions for responsive layout
  private cameraWidth!: number;
  private cameraHeight!: number;
  private centerX!: number;
  private centerY!: number;

  constructor() {
    super({ key: 'GameScene' });
    // Initialize TicTaps connector if needed (following FingerFrenzy pattern)
    // this.ticTaps = new TicTapsConnector();
  }

  init(){
    // Get game config from registry
    const gameConfig = this.registry.get('gameConfig') as BingoConfig;
    this.socketClient = gameConfig?.socketClient || null;
    this.gameId = gameConfig?.gameId || 'bingo';
    this.roomId = gameConfig?.roomId || 'room-' + Date.now().toString(36);

    this.UIContainer = this.add.container(0, 0);

    // Setup socket event listeners if available
    if (this.socketClient) {
      this.setupSocketEventListeners();
    }

    // Initialize managers
    this.scoreManager = new ScoreManager(this, {
      initialScore: 0,
      fontSize: '80px',
      scoreColor: '#33DDFF'
    });
    this.livesManager = new LivesManager(this);
  }

  /**
   * Setup socket event listeners for server communication
   */
  private setupSocketEventListeners(): void {
    if (!this.socketClient) return;

    // Add custom event listeners for game-specific handling
    this.socketClient.addCustomEventListener('started', (data: any) => {
      console.log('Bingo game started by server:', data);
      console.log('Received bingoGameState:', data.bingoGameState);

      // Sync game state from server
      if (data.bingoGameState) {
        console.log('Syncing bingo card from server:', data.bingoGameState.bingoCard);
        this.syncGameStateFromServer(data.bingoGameState);
      } else {
        console.error('No bingoGameState received from server!');
      }

      // Start the local game UI with server-provided data
      this.startGame();
    });

    this.socketClient.addCustomEventListener('action_result', (data: any) => {
      console.log('Action result from server:', data);
      if (data.actionType === 'cell_mark') {
        this.handleCellMarkResult(data.data);
      }
    });

    this.socketClient.addCustomEventListener('number_called', (data: any) => {
      console.log('Number called by server:', data);
      this.handleNumberCalled(data.calledNumber, data.gameState);
    });

    this.socketClient.addCustomEventListener('ended', (data: any) => {
      console.log('Game ended by server:', data);
      this.endGame();
    });

    this.socketClient.addCustomEventListener('game_error', (data: any) => {
      console.error('Game error from server:', data);
    });

    console.log('Socket event listeners setup for Bingo GameScene');
  }

  /**
   * Sync game state from server (following FingerFrenzy pattern)
   */
  private syncGameStateFromServer(serverGameState: any): void {
    console.log('Syncing game state from server:', serverGameState);

    // Update local bingo card with server state
    if (serverGameState.bingoCard) {
      console.log('Setting bingo card from server:', serverGameState.bingoCard);
      this.bingoCard = serverGameState.bingoCard;
    } else {
      console.warn('No bingoCard in server game state');
    }

    // Server manages called numbers and call order
    // Client only needs the bingo card for visual representation
  }

  /**
   * Handle cell mark result from server using simplified ID system
   */
  private handleCellMarkResult(data: any): void {
    const { cellId, isCorrect, points, newScore, newLives, gameEnded, winResult, gameState, matchedCalledNumber } = data;

    // Update local game state
    if (gameState) {
      this.syncGameStateFromServer(gameState);
    }

    
    // Find and update the cell by ID
    const cell = this.findBingoCellById(cellId);

    if (cell && isCorrect) {
      
      this.scoreManager.addPoints(points, {
        startX: cell.x,
        startY: cell.y,
        points: points
      });

      cell.mark();
      // Play success sound
      this.sound.play('match');

      // Remove the specific called number from the right panel as instructed by server
      if (matchedCalledNumber) {
        const calledNumberId = `${matchedCalledNumber.column}${matchedCalledNumber.number}`;
        const matchingNumber = this.rightNumbers.find(item => item.name === calledNumberId);

        if (matchingNumber) {
          // Remove any highlight effects (if they exist)
          this.children.list.forEach(child => {
            if (child.type === 'Graphics' &&
                (child as any).x === cell.x - 40 &&
                (child as any).y === cell.y - 40) {
              child.destroy();
            }
          });

          // Remove the matching number from the array
          this.rightNumbers = this.rightNumbers.filter(item => item !== matchingNumber);

          // Destroy the matching number
          matchingNumber.destroy();
        }
      }
    } else if (!isCorrect) {
      // Play error sound
      this.sound.play('wrong');

      gameActions.updateLives(data.newLives);
      if(cell && this.UIContainer){
        this.livesManager.deductHeart(
          cell.x,
          cell.y,
          this.UIContainer
        );
      }
    }

    // Handle game end
    if (gameEnded) {
      if (winResult?.hasWon) {
        this.handleBingoWin(winResult);
      } else {
        this.endGame();
      }
    }
  }

  /**
   * Handle number called from server 
   */
  private handleNumberCalled(calledNumber: any, gameState: any): void {
    // Sync game state
    this.syncGameStateFromServer(gameState);

    // Add the called number to the right panel
    this.addRightItemFromServer(calledNumber);
  }

  /**
   * Add right item from server-called number 
   */
  private addRightItemFromServer(calledNumber: any): void {
    if (this.gameEnd) return;

    // Play sound
    this.sound.play('number-appear');

    // Move existing right numbers down
    this.moveExistingRightItems();

    // Get position for new item
    const position = this.getRightPosition(4);

    // Create new right number with the called number
    const rightNumber = new RightNumber(
      this,
      position.x,
      position.y,
      4,
      calledNumber.column,
      calledNumber.number
    );

    // Add to right panel
    this.rightNumbers.push(rightNumber);
  }

  // findBingoCellByName method moved to avoid duplication

  create() {
    // Set camera dimensions and center points
    this.cameraWidth = this.cameras.main.width;
    this.cameraHeight = this.cameras.main.height;
    this.centerX = this.cameraWidth / 2;
    this.centerY = this.cameraHeight / 2;

    // Initialize UI-only variables
    this.gameEnd = false;
    this.bgoCells = [];
    this.rightNumbers = [];

    // Calculate right positions for UI
    this.rightPositions = this.calculateRightPositions();

    // Setup UI elements
    this.createBackground();

    // Start countdown and then wait for server to start game
    this.startCountdown();
  }

  shutdown() {
    // Clean up managers
    if (this.scoreManager) {
      this.scoreManager.destroy();
    }
  }

  /**
   * Create game background - using game_bg.png
   */
  createBackground() {
    // Add the game background image
    this.add.image(0, 0, 'game_background')
        .setOrigin(0, 0)
        .setDisplaySize(this.cameraWidth, this.cameraHeight);
  }

  /**
   * Create countdown overlay (initially hidden)
   * Using countdown images like in matching mayhem
   */
  createCountdownOverlay() {
    // Create countdown panel container
    this.countdownPanel = this.add.container(0, 0);
    this.countdownPanel.setDepth(2); // Ensure countdown panel is above everything

    // Add semi-transparent overlay
    const overlay = this.add.rectangle(
      0, 0, 
      this.cameraWidth, 
      this.cameraHeight, 
      0x000000, 0.7
    ).setOrigin(0, 0);
    this.countdownPanel.add(overlay);

    // Create countdown image (initially hidden, will be updated during countdown)
    this.countdownText = this.add.image(
      this.centerX,
      this.centerY,
      'countdown-3'
    ).setScale(0).setOrigin(0.5);
    this.countdownPanel.add(this.countdownText);
  }

  private async startCountdown(): Promise<void> {
    for (let i = 0; i < 4; i++) {
      // Play sound
      try {
        this.sound.play(i === 3 ? 'go' : 'countdown');
      } catch (error) {
        console.warn('Sound playback failed:', error);
      }

      await new Promise<void>((resolve) => {
        this.time.delayedCall(1300, () => resolve());
      });
    }

    // Send game start event to server using proper format
    if (this.socketClient && this.socketClient.isConnected()) {
      this.socketClient.startGame();
    } else {
      console.error('No server connection available - cannot start Bingo game');
    }
  }

  /**
   * Start game (called when server confirms game start)
   */
  private startGame() {
    // Ensure we have bingo card data from server
    if (!this.bingoCard || this.bingoCard.length === 0) {
      console.error('No bingo card data received from server!');
      return;
    }

    console.log('Starting game with server-provided bingo card:', this.bingoCard);

    // Create bingo board UI with server-provided card
    this.createBingoBoard();

    // Server will handle number calling and timing automatically
  }

  // Local game logic methods removed - now handled by server
  // Card generation, number calling, and game state management moved to BingoController

  /**
   * Create the bingo board with server-provided data
   */
  createBingoBoard() {
    console.log('Creating bingo board with server data:', this.bingoCard);

    if (!this.bingoCard || this.bingoCard.length === 0) {
      console.error('Cannot create bingo board: no server data available');
      return;
    }

    const cellSize = 80;
    const cellSpacing = 10;
    const totalWidth = (cellSize + cellSpacing) * 5 - cellSpacing;
    const startX = this.centerX - totalWidth / 2; // Center the board horizontally

    // Calculate vertical positions based on camera height
    const boardCenterY = this.centerY + 150; // Slightly below center
    const headerY = boardCenterY - (cellSize + cellSpacing) * 2.5;
    const gridStartY = headerY + (cellSize + cellSpacing);

    // Create the BINGO header row
    for (let col = 0; col < 5; col++) {
      const x = startX + col * (cellSize + cellSpacing) + cellSize / 2;
      const y = headerY;

      // Create a graphics object for the rounded rectangle
      const graphics = this.add.graphics();

      // Use gradient fill for the header
      graphics.fillGradientStyle(
          0x3066FF, // Top-left: blue
          0x4752FF, // Top-right: blue-purple
          0x5E4DFF, // Bottom-right: more purple
          0x215EFF, // Bottom-left: blue
          1
      );
      graphics.lineStyle(3, 0x00E5AE, 1);

      // Draw rounded rectangle centered at (x, y)
      graphics.fillRoundedRect(x - cellSize/2, y - cellSize/2, cellSize, cellSize, 16);
      graphics.strokeRoundedRect(x - cellSize/2, y - cellSize/2, cellSize, cellSize, 16);

      // Add letter
      this.add.text(
          x,
          y,
          GameScene.BINGO_COLUMNS[col],
          {
            fontFamily: '"TT Neoris", Arial, sans-serif',
            fontSize: '48px',
            color: '#FFFFFF',
            align: 'center',
            fontStyle: 'bold',
            stroke: '#000000',
            strokeThickness: 3,
            shadow: { offsetX: 2, offsetY: 2, color: '#000000', blur: 2, fill: true }
          }
      ).setOrigin(0.5);
    }

    // Create 5x5 grid of bingo cells (excluding the header row)
    for (let row = 0; row < 5; row++) {
      for (let col = 0; col < 5; col++) {
        const x = startX + col * (cellSize + cellSpacing) + cellSize / 2;
        const y = gridStartY + row * (cellSize + cellSpacing);

        // Get cell data from generated bingo card
        const cellData = this.bingoCard[row][col];

        // Create cell with proper bingo data
        const cell = new BingoCell(
            this,
            x,
            y,
            cellData.column,
            cellData.isFree ? 0 : cellData.number, // Use 0 for FREE space
            cellData.isFree
        );

        // Hide letter for main board (except FREE space)
        if (!cellData.isFree) {
          cell.letterText.alpha = 0;
        }

        // Initially set scale to 0 for animation
        cell.setScale(0);

        // Add to scene and store reference
        this.bgoCells.push(cell);

        // Create staggered animation for each cell based on row and column
        // This creates a wave-like effect as cells appear
        this.time.delayedCall(150 * row + 50 * col, () => {
          this.tweens.add({
            targets: cell,
            scale: 1,
            duration: 300,
            ease: 'Back.Out'
          });
        });
      }
    }
  }

  /**
   * Calculate positions for right panel items
   */
  private calculateRightPositions(): { x: number; y: number }[] {
    const positions = [];
    const cellSize = 80;
    const cellSpacing = 10;
    const totalWidth = (cellSize + cellSpacing) * 5 - cellSpacing;
    const startX = this.centerX - totalWidth / 2; // Center horizontally

    // Position between header and timer
    const panelY = this.centerY - 180;

    // Create positions for 5 items (0-4 indices)
    for (let i = 0; i < 5; i++) {
      positions.push({
        x: startX + i * (cellSize + cellSpacing) + cellSize / 2,
        y: panelY
      });
    }

    return positions;
  }

  /**
   * Get position for right panel item by index
   * @param {number} index - Position index
   * @returns {object} Position with x and y coordinates
   */
  public getRightPosition(index: number): { x: number; y: number } {
    if (index >= 0 && index < this.rightPositions.length) {
      return this.rightPositions[index];
    }
    return { x: this.centerX + 100, y: this.centerY };
  }

  // Number calling logic removed - now handled by server

  /**
   * Move existing right items down
   */
  moveExistingRightItems() {
    for (const item of this.rightNumbers) {
      // Decrease index
      item.moveToPosition(item.rightIndex - 1);
    }

    // Filter out destroyed items
    this.rightNumbers = this.rightNumbers.filter(item => item.rightIndex >= 0);
  }

  /**
   * Check if a cell matches any right panel item (now uses server validation)
   * @param {BingoCell} cell - The cell to check
   */
  public checkForMatch(cell: BingoCell): void {
    if (this.gameEnd) return;

    // If we have a socket client, send the cell mark to server for validation
    if (this.socketClient && this.socketClient.isConnected()) {
      this.socketClient.sendGameAction('cell_mark', {
        cellId: cell.name  // Using cell.name as the ID
      });
      return;
    }

    // Fallback to local validation (original logic) - for offline mode
    this.checkForMatchLocal(cell);
  }

  /**
   * Local cell matching logic (fallback for offline mode)
   * NOTE: This is a simplified fallback - in production, server should always handle game logic
   */
  private checkForMatchLocal(cell: BingoCell): void {
    // Find matching right number
    const matchingNumber = this.rightNumbers.find(item => item.name === cell.name);

    if (matchingNumber) {
      // Mark the cell (visual only)
      cell.mark();

      // Remove any highlight effects (if they exist)
      this.children.list.forEach(child => {
        if (child.type === 'Graphics' &&
            (child as any).x === cell.x - 40 &&
            (child as any).y === cell.y - 40) {
          child.destroy();
        }
      });

      // Play sound
      this.sound.play('match');

      // Remove the matching number from the array (visual only)
      this.rightNumbers = this.rightNumbers.filter(item => item !== matchingNumber);

      // Destroy the matching number (visual only)
      matchingNumber.destroy();

      // NOTE: Scoring and win checking should be handled by server
      // This is just a basic offline fallback for visual feedback
    }
    else{
      this.sound.play('wrong');
      // NOTE: Lives management should be handled by server
    }
  }

  /**
   * Handle a bingo win
   */
  private handleBingoWin(winResult: WinResult): void {
    if (this.gameEnd) return;

    // Set game end flag
    this.gameEnd = true;
    this.endGame();

    // Create win announcement
    this.createWinAnnouncement(winResult);

    // Create celebration effects
    this.createCelebrationEffects();


    // // Transition to GameEndScene after a brief delay
    // this.time.delayedCall(1500, () => {
    //   this.scene.start('GameEndScene',
    //     {
    //       score: this.scoreManager.getScore(),
    //       winPattern: 'timeout'
    //     });
    // });
  }

  /**
   * Create win announcement display
   */
  private createWinAnnouncement(winResult: WinResult): void {
    const announcement = this.add.text(
      this.centerX,
      this.centerY - 100,
      PATTERN_DISPLAY_NAMES[winResult.pattern!],
      {
        fontFamily: '"TT Neoris", Arial, sans-serif',
        fontSize: '64px',
        color: '#FFD700',
        align: 'center',
        fontStyle: 'bold',
        stroke: '#000000',
        strokeThickness: 4,
        shadow: { offsetX: 3, offsetY: 3, color: '#000000', blur: 5, fill: true }
      }
    ).setOrigin(0.5).setDepth(10);

    // Animate the announcement
    announcement.setScale(0);
    this.tweens.add({
      targets: announcement,
      scale: 1,
      duration: 500,
      ease: 'Back.Out',
      onComplete: () => {
        // Pulse animation
        this.tweens.add({
          targets: announcement,
          scale: { from: 1, to: 1.1 },
          duration: 800,
          yoyo: true,
          repeat: -1,
          ease: 'Sine.easeInOut'
        });
      }
    });
  }

  // NOTE: Win checking methods removed - server handles all game logic validation

  /**
   * Find a bingo cell by ID (simplified system)
   * @param {string} id - The ID of the cell (e.g., "B12", "NFREE")
   * @returns {BingoCell|null} The matching cell or null if not found
   */
  public findBingoCellById(id: string): BingoCell | null {
    return this.bgoCells.find(cell => cell.name === id) || null;
  }

  /**
   * Find a bingo cell by name (legacy method)
   * @param {string} name - The name of the cell (e.g., "B12")
   * @returns {BingoCell|null} The matching cell or null if not found
   */
  public findBingoCellByName(name: string): BingoCell | null {
    return this.bgoCells.find(cell => cell.name === name) || null;
  }


  /**
   * End the game (called when timer runs out or no more numbers available)
   * Equivalent to when GameEnd is set to true in Unity
   */
  endGame() {
    if (this.gameEnd) return;

    // Set game end flag
    this.gameEnd = true;
    gameActions.endGame();

    this.sound.play('timeout');

    // Create timeout announcement
    // this.createTimeoutAnnouncement();

    // Create celebration effects
    this.createCelebrationEffects();

    // // Transition to GameEndScene after a brief delay
    // this.time.delayedCall(1500, () => {
    //   this.scene.start('GameEndScene', { score: this.scoreManager.getScore(), winPattern: 'timeout' });
    // });
  }

  /**
   * Create celebration effects for game end
   * Equivalent to HandleGameEnd in Unity
   */
  createCelebrationEffects() {
    // Play win sound
    this.sound.play('win');

    // Create particles on each cell
    for (const cell of this.bgoCells) {
      // Mark unmarked cells
      if (!cell.marked) {
        cell.mark();
      }

      // Add delayed win particles with random delay
      this.time.delayedCall(Math.random() * 1000, () => {
        cell.createWinParticles();
      });
    }
  }
}
