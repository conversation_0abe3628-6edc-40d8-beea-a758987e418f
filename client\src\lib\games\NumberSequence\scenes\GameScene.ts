import * as Phaser from 'phaser';
import TicTapsConnector from '../utils/TicTapsConnector';
import GameConfig from '../config/GameConfig';
import NumberObject from '../objects/NumberObject';
import ScoreManager from '../managers/ScoreManager';
import TimerManager from '../managers/TimerManager';
import LivesManager from '../managers/LivesManager';
import TimerBarUI from '../ui/TimerBarUI';
import type {
  CountPerRound,
  Position
} from '../types/types';

export default class GameScene extends Phaser.Scene {
  private ticTaps!: TicTapsConnector;
  private gamePanel!: Phaser.GameObjects.Container;
  private countdownPanel!: Phaser.GameObjects.Container;
  private socketClient: any;

  private numberObjects: NumberObject[] = [];
  private currentIndex: number = 0;
  private isLocked: boolean = false;
  private roundText!: Phaser.GameObjects.Image;
  private currentSequence: number[] = [];

  private scoreManager!: ScoreManager;
  private timerManager!: TimerManager;
  private livesManager!: LivesManager;
  private timerBarUI!: TimerBarUI;

  private line!: Phaser.GameObjects.Graphics;
  private round: number = 1;
  private countPerRound: CountPerRound = {
    1: 5,
    2: 7,
    3: 9,
    4: 12
  };
  private confettiEmitter!: Phaser.GameObjects.Particles.ParticleEmitter;

  constructor() {
    super('GameScene');
  }

  /**
   * Helper method to play sound effects with proper error handling
   */
  private playSound(soundEffect: string): void {
    try {
      this.sound.play(soundEffect);
    } catch (error) {
      console.warn('Sound playback failed:', error);
    }
  }

  init(data: { socketClient: any }): void {
    // Store socket client reference
    this.socketClient = data.socketClient;

    // Reset all game state variables when scene initializes
    this.currentIndex = 0;
    this.isLocked = false;
    this.round = 1;

    // Initialize managers
    this.scoreManager = new ScoreManager(this, {
      initialScore: 0,
      fontSize: '80px',
      scoreColor: '#33DDFF'
    });

    this.timerManager = new TimerManager(this, {
      duration: GameConfig.GAME_DURATION,
      warningThreshold: 5
    });

    this.livesManager = new LivesManager(this, {
      initialLives: 3
    });

    // Setup socket event listeners
    // Handle game initialization
    this.socketClient.addCustomEventListener('initialized', (data: any) => {
      console.log('Number Sequence game initialized by server:', data);

      // Initialize game with first sequence
      if (data.currentSequence) {
        this.currentSequence = data.currentSequence;
        this.generateNumbers(data.currentSequence);
      }

      // Start countdown after initialization
      this.startCountdown();
    });

    // Handle game start
    this.socketClient.addCustomEventListener('started', (data: any) => {
      console.log('Number Sequence game started by server:', data);

      // Show game panel and start timer
      this.countdownPanel.visible = false;
      this.gamePanel.visible = true;
      this.startTimer();
    });

    // Handle action results
    this.socketClient.addCustomEventListener('action_result', (data: any) => {
      if (data.isCorrect) {
        const numberObj = this.numberObjects.find(obj => obj.numberValue === this.currentIndex);
        if (numberObj) {
          numberObj.setActive(true);
          this.updateLine(numberObj.x, numberObj.y);
          this.playSound('collect');
          this.currentIndex++;
        }

        // Update game state
        this.scoreManager.setScore(data.newScore);
        this.updateGradientRoundText(`${data.currentIndex} to ${data.nextSequence ? data.nextSequence.length : this.currentSequence.length}`);

        // If we got a new sequence, update the game
        if (data.nextSequence) {
          this.currentSequence = data.nextSequence;
          this.generateNumbers(data.nextSequence);
        }
      } else {
        const numberObj = this.numberObjects.find(obj => obj.numberValue === this.currentIndex);
        if (numberObj) {
          numberObj.setError();
          this.playSound('error');
        }

        this.livesManager.deductHeart();
      }
    });

    this.timerBarUI = new TimerBarUI(this, {
      width: this.cameras.main.width * 0.8,
      height: 35,
      x: this.cameras.main.width / 2,
      y: this.cameras.main.height * 0.07
    });

    // Set up timer events
    this.timerManager.on('timeUp', () => this.timeOut());
    this.timerManager.on('timerUpdate', (state) => {
      this.timerBarUI.updateProgress(state.progress);
    });

    this.livesManager.on('heartDeducted', (lives) => {
      if (lives === 0) {
        this.timeOut();
      }
    });
  }

  create(): void {
    // Initialize TicTaps connector
    this.ticTaps = new TicTapsConnector();

    // Add game background image
    this.add.image(this.cameras.main.width / 2, this.cameras.main.height / 2, 'game_background')
      .setOrigin(0.5)
      .setDisplaySize(this.cameras.main.width, this.cameras.main.height);

    // Create main panels
    this.createCountdownPanel();
    this.createGamePanel();

    // Create line graphics for drawing mode
    this.line = this.add.graphics();

    // Create confetti particle system
    this.createConfettiSystem();

    // Initialize game first (client load)
    this.time.delayedCall(100, () => {
      this.initializeGame();
    });
  }

  shutdown(): void {
    // Clean up managers
    if (this.scoreManager) {
      this.scoreManager.destroy();
    }

    if (this.timerManager) {
      this.timerManager.destroy();
    }

    if (this.timerBarUI) {
      this.timerBarUI.destroy();
    }

    if (this.livesManager) {
      this.livesManager.destroy();
    }

    // Stop confetti if running
    if (this.confettiEmitter) {
      this.confettiEmitter.stop();
    }

    // Clean up number objects
    this.numberObjects.forEach(obj => obj.destroy());
    this.numberObjects = [];
  }

  private createUI(): void {
    const { width, height } = this.cameras.main;

    const timerContainer = this.add.container(0, 0);
    this.gamePanel.add(timerContainer);

    // Create timer bar UI
    this.timerBarUI.create(timerContainer);

    // Create timer text at the right circle position
    const rightCirclePos = this.timerBarUI.getRightCirclePosition();
    this.timerManager.createUI(rightCirclePos.x, rightCirclePos.y, timerContainer);

    // Create score display
    const timerBarY = height * 0.07;
    this.scoreManager.createUI(width / 2, timerBarY + 120, timerContainer);

    // Create lives display
    this.livesManager.createUI(width / 2, timerBarY + 50, timerContainer);
  }

  private createCountdownPanel(): void {
    const { width, height } = this.cameras.main;
    this.countdownPanel = this.add.container(width / 2, height / 2);

    const countdownBg = this.add.graphics();
    countdownBg.fillStyle(0x000000, 0.8);
    countdownBg.fillRect(-width/2, -height/2, width, height);
    this.countdownPanel.add(countdownBg);

    const countdownImage = this.add.image(0, 0, 'countdown-3').setScale(0.27);
    this.countdownPanel.add(countdownImage);

    this.countdownPanel.setData('image', countdownImage);
  }

  private createGamePanel(): void {
    this.gamePanel = this.add.container(0, 0);
    this.gamePanel.visible = false;

    // Create the new top timer bar
    // this.createTopTimerBar();
    this.createUI();

    // Create gradient round indicator at bottom left
    this.createGradientRoundText();

    this.gamePanel.add([]);
  }

  /**
   * Create gradient round text similar to EndGameScene
   */
  private createGradientRoundText(): void {
    // Create the initial gradient text
    this.updateGradientRoundText('0 to 6');
  }

  /**
   * Update the gradient round text with new content
   */
  private updateGradientRoundText(text: string): void {
    const { height } = this.cameras.main;

    // Generate a unique texture key based on the text
    const textureName = 'roundText-' + text.replace(/\s+/g, '-');

    // Remove any previous version of this texture if it exists
    if (this.textures.exists(textureName)) {
      this.textures.remove(textureName);
    }

    // Calculate canvas size based on text length
    const fontSize = 24;
    const width = Math.max(120, text.length * fontSize * 0.6);
    const textHeight = fontSize * 1.5;

    // Create a canvas texture
    const textCanvas = this.textures.createCanvas(textureName, width, textHeight);
    if (!textCanvas) {
      console.error('Failed to create gradient round text canvas');
      // Fallback to regular text if canvas fails
      if (this.roundText) {
        this.roundText.destroy();
      }
      this.roundText = this.add.text(50, height - 20, text, {
        fontSize: '24px',
        fontFamily: 'Arial',
        color: '#999999'
      }).setOrigin(0, 1) as any;
      this.gamePanel.add(this.roundText);
      return;
    }

    const context = textCanvas.getContext();

    // Create a gradient - match the exact colors from EndGameScene
    const gradient = context.createLinearGradient(0, 0, width, textHeight * 0.5);
    gradient.addColorStop(0, '#32c4ff');  // Cyan
    gradient.addColorStop(0.5, '#7f54ff'); // Purple in middle
    gradient.addColorStop(1, '#b63efc');   // Pink

    // Set text properties
    context.font = `bold ${fontSize}px Arial`;
    context.textAlign = 'left';
    context.textBaseline = 'middle';

    // Fill with gradient
    context.fillStyle = gradient;
    context.fillText(text, 0, textHeight / 2);

    // Update the texture
    textCanvas.refresh();

    // Remove old round text if it exists
    if (this.roundText) {
      this.roundText.destroy();
    }

    // Create and add new gradient text image
    this.roundText = this.add.image(50, height - 20, textureName).setOrigin(0, 1);
    this.gamePanel.add(this.roundText);
  }

  /**
   * Initialize the game (called at client load)
   */
  private initializeGame(): void {
    console.log('Initializing Number Sequence game...');

    // Send initialization request to server
    if (this.socketClient && this.socketClient.isConnected()) {
      this.socketClient.initGame();
    } else {
      console.error('No server connection available - cannot initialize game');
    }
  }

  private async startCountdown(): Promise<void> {
    const countdownImages = ['countdown-3', 'countdown-2', 'countdown-1', 'countdown-go'];
    const countdownImage = this.countdownPanel.getData('image');

    for (let i = 0; i < countdownImages.length; i++) {
      await this.playCountdownStep(countdownImage, countdownImages[i], i === countdownImages.length - 1);
    }

    // Send game start event to server
    if (this.socketClient && this.socketClient.isConnected()) {
      this.socketClient.startGame();
    } else {
      console.error('No server connection available - cannot start game');
    }
  }

  private playCountdownStep(image: Phaser.GameObjects.Image, texture: string, isGo: boolean): Promise<void> {
    return new Promise((resolve) => {
      image.setTexture(texture);
      image.setScale(0);

      this.playSound(isGo ? 'go' : 'countdown');

      this.tweens.add({
        targets: image,
        scale: 0.27,
        duration: 300,
        ease: 'Back.easeOut',
        onComplete: () => {
          this.time.delayedCall(700, () => {
            this.tweens.add({
              targets: image,
              scale: 0,
              duration: 300,
              ease: 'Back.easeIn',
              onComplete: () => resolve()
            });
          });
        }
      });
    });
  }

  private generateNumbers(sequence: number[]): void {
    // Clear existing numbers
    this.numberObjects.forEach(obj => obj.destroy());
    this.numberObjects = [];
    this.currentIndex = 0;
    this.line.clear();

    // Update round indicator
    this.updateGradientRoundText(`0 to ${sequence.length}`);

    // Define possible positions
    const positions = this.generatePositions(sequence.length);

    // Create number objects one by one with delays
    sequence.forEach((num, i) => {
      this.time.delayedCall(i * 100, () => { // 100ms delay between each spawn
        const numberObj = new NumberObject(this, positions[i].x, positions[i].y, num);
        this.numberObjects.push(numberObj);
      });
    });
  }

  private generatePositions(count: number): Position[] {
    const { width, height } = this.cameras.main;

    // Circle size and minimum spacing requirements
    const circleRadius = GameConfig.CIRCLE_RADIUS;
    const minDistance = circleRadius * 2 + 20;

    // Define game area boundaries relative to screen size
    const gameAreaStart = height * 0.3;   // Start below the timer area (40% from top)
    const gameAreaEnd = height * 0.95;    // End before bottom indicator (95% from top)
    const leftBoundary = width * 0.06;    // 6% margin
    const rightBoundary = width * 0.94;   // 94% margin

    const positions: { x: number, y: number }[] = [];

    // Function to check if a position is valid (doesn't overlap with existing circles)
    const isPositionValid = (newX: number, newY: number): boolean => {
      // Check boundaries
      if (newX - circleRadius < leftBoundary ||
          newX + circleRadius > rightBoundary ||
          newY - circleRadius < gameAreaStart ||
          newY + circleRadius > gameAreaEnd) {
        return false;
      }

      // Check distance from all existing positions
      for (const pos of positions) {
        const distance = Math.sqrt(
            Math.pow(newX - pos.x, 2) + Math.pow(newY - pos.y, 2)
        );
        if (distance < minDistance) {
          return false;
        }
      }
      return true;
    };

    // Generate positions with collision detection
    let attempts = 0;
    const maxAttempts = 1000; // Prevent infinite loops

    while (positions.length < count && attempts < maxAttempts) {
      // Generate random position within boundaries
      const randomX = leftBoundary + circleRadius +
          Math.random() * (rightBoundary - leftBoundary - circleRadius * 2);
      const randomY = gameAreaStart + circleRadius +
          Math.random() * (gameAreaEnd - gameAreaStart - circleRadius * 2);

      if (isPositionValid(randomX, randomY)) {
        positions.push({ x: randomX, y: randomY });
      }
      attempts++;
    }

    // If we couldn't generate enough positions with pure random, use a grid-based fallback
    if (positions.length < count) {
      console.warn('Using grid fallback for remaining positions');
      return this.generateGridBasedPositions(count);
    }

    return positions;
  }

  private generateGridBasedPositions(count: number): { x: number, y: number }[] {
    const { width, height } = this.cameras.main;

    // Fallback to grid-based positioning if random positioning fails
    const circleDiameter = GameConfig.CIRCLE_RADIUS * 2;
    const gridWidth = Math.min(5, Math.floor(width / circleDiameter)); // Dynamic based on screen width
    const gridHeight = Math.ceil(count / gridWidth);

    // Calculate total width and height needed
    const totalWidth = gridWidth * circleDiameter;
    const totalHeight = gridHeight * circleDiameter;

    // Calculate starting positions to center the grid
    const startX = (width - totalWidth) / 2 + circleDiameter / 2;
    const gameAreaHeight = height * 0.75;  // Bottom 75% of screen
    const gameAreaStart = height * 0.2;    // Start below the timer area
    const availableHeight = gameAreaHeight;
    const startY = gameAreaStart + (availableHeight - totalHeight) / 2;

    const gridPositions = [];
    for (let i = 0; i < count; i++) {
      const row = Math.floor(i / gridWidth);
      const col = i % gridWidth;
      gridPositions.push({
        x: startX + col * circleDiameter,
        y: startY + row * circleDiameter
      });
    }

    // Shuffle the grid positions
    const shuffled = [...gridPositions].sort(() => Math.random() - 0.5);
    return shuffled;
  }

  private startTimer(): void {
    this.timerManager.start();
  }

  private timeOut(): void {
    this.isLocked = true;

    // Stop timer
    this.timerManager.stop();

    this.playSound('timeout');

    // Play confetti
    this.confettiEmitter.start();

    // Transition to GameEndScene after a brief delay
    this.time.delayedCall(1500, () => {
      this.scene.start('GameEndScene', { score: this.scoreManager.getScore() });
    });
  }

  public handleNumberClick(numberObject: NumberObject): void {
    if (this.isLocked) return;

    // Send number selection to server
    if (this.socketClient) {
      this.socketClient.sendNumberSelect(
        'numbers',
        'default-room',
        numberObject.numberValue
      );
    }
  }

  private handleCorrectNumber(numberObject: NumberObject): void {
    numberObject.setActive(true);
    this.updateLine(numberObject.x, numberObject.y);

    this.playSound('collect');

    // Add points using ScoreManager with animation
    this.scoreManager.addPoints(this.round, {
      startX: numberObject.x,
      startY: numberObject.y,
      points: this.round
    });

    // Update game state immediately (before animation)
    this.currentIndex++;

    // Update round indicator
    this.updateGradientRoundText(`${this.currentIndex} to ${this.countPerRound[this.round]}`);

    // Check for round/game completion immediately
    const roundCompleted = this.currentIndex === this.countPerRound[this.round];

    // Animate the number object disappearing
    numberObject.playDisappearAnimation(() => {
      // Animation complete - only handle visual cleanup here
      if (roundCompleted) {
        if (this.round > 3) {
          this.gameComplete();
        } else {
          this.roundComplete();
        }
      }
    });
  }

  private handleWrongNumber(numberObject: NumberObject): void {
    numberObject.setError();
    this.playSound('error');

    this.livesManager.deductHeart(
      numberObject.x,
      numberObject.y
    );
    
    //
    // LOCKING DISABLED FOR NO DISRUPTION
    //
    // this.isLocked = true;
    // // Unlock after the error animation
    // this.time.delayedCall(500, () => {
    //   numberObject.clearError();
    //   this.isLocked = false;
    // });
  }

  private updateLine(x: number, y: number): void {
    if (this.currentIndex === 1) {
      this.line.lineStyle(4, 0x00ccff);
      this.line.moveTo(this.numberObjects[0].x, this.numberObjects[0].y);
    }

    this.line.lineTo(x, y);
  }

  private roundComplete(): void {
    this.round++;

    if (this.round > Object.keys(this.countPerRound).length ) {
      this.gameComplete();
    } else {
      // Get the position of the last clicked number for confetti
      const lastNumberIndex = this.currentIndex - 1;
      const lastNumberObject = this.numberObjects[lastNumberIndex];

      if (lastNumberObject) {
        // Move confetti emitter to the last clicked number's position
        this.confettiEmitter.setPosition(lastNumberObject.x, lastNumberObject.y);

        // Start confetti explosion from that point
        this.confettiEmitter.explode(25); // Burst of 25 particles
      }

      this.playSound('complete');

      // Next sequence will be sent by server
    }
  }

  private gameComplete(): void {
    this.isLocked = true;

    // Stop timer
    this.timerManager.stop();

    // Get the position of the last clicked number for confetti
    const lastNumberIndex = this.currentIndex - 1;
    const lastNumberObject = this.numberObjects[lastNumberIndex];

    if (lastNumberObject) {
      // Move confetti emitter to the last clicked number's position
      this.confettiEmitter.setPosition(lastNumberObject.x, lastNumberObject.y);

      // Start confetti explosion from that point
      this.confettiEmitter.explode(25); // Burst of 25 particles
    }

    // Get timer state for score calculation
    const timerState = this.timerManager.getState();
    const timeRemainingInSeconds = Math.floor(timerState.timeRemaining);

    console.log('Time remaining:', timeRemainingInSeconds);

    // Ensure time remaining is at least 1 second for score multiplication
    const timeMultiplier = Math.max(1, timeRemainingInSeconds);

    // Only apply time multiplier if the game was completed within the time limit
    const currentScore = this.scoreManager.getScore();
    const finalScore = timerState.timeRemaining > 0
        ? currentScore * timeMultiplier
        : currentScore;

    this.playSound('complete');

    // Wait for confetti animation to finish before transitioning
    // Confetti particles live for 1500-2500ms, so wait 3000ms to be safe
    this.time.delayedCall(3000, () => {
      this.scene.start('GameEndScene', { score: finalScore });
    });
  }

  private createConfettiSystem(): void {
    const { width } = this.cameras.main;

    // Create custom particle texture
    const texture = this.textures.createCanvas('particleTexture', 10, 10);
    if (!texture) {
      console.warn('Failed to create particle texture, using fallback');
      // Fallback to using no texture (empty string)
      this.confettiEmitter = this.add.particles(width / 2, -50, '', {
        speed: { min: 150, max: 350 },
        scale: { start: 0.8, end: 0 },
        lifespan: { min: 1500, max: 2500 },
        gravityY: 400,
        rotate: { min: -180, max: 180 },
        frequency: 50,
        quantity: 3,
        tint: [0xff0000, 0x00ff00, 0x0000ff, 0xffff00, 0xff00ff, 0x00ffff],
        blendMode: 'ADD',
        emitting: false
      });
      return;
    }

    const context = texture.getContext();
    context.fillStyle = '#ffffff';
    context.fillRect(0, 0, 10, 10);
    texture.refresh();

    // Create confetti emitter with settings optimized for explosions
    this.confettiEmitter = this.add.particles(width / 2, -50, 'particleTexture', {
      speed: { min: 150, max: 350 }, // Outward explosion speed
      scale: { start: 0.8, end: 0 }, // Start bigger, shrink to nothing
      lifespan: { min: 1500, max: 2500 }, // How long particles live
      gravityY: 400, // Gravity pulls them down after explosion
      scaleX: {
        onUpdate: (_particle, _key, t) => {
          return Math.sin((t * Math.PI) * 8); // Oscillating scale effect
        },
      },
      rotate: { min: -180, max: 180 }, // Random rotation
      frequency: 50, // For continuous emission (not used for explode)
      quantity: 3, // For continuous emission (not used for explode)
      tint: [0xff0000, 0x00ff00, 0x0000ff, 0xffff00, 0xff00ff, 0x00ffff],
      blendMode: 'ADD',
      emitting: false // We'll use explode() method instead
    });
  }
}
