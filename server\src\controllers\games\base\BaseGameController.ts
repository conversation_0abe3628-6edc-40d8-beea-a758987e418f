import type { Server, Socket } from 'socket.io';
import type { GameService } from '../../../services/gameService';
import { logger } from '../../../utils/logger';
import type {
  GameInitResult,
  GameStartData,
  GameEndData,
  GameActionData,
  EndReason,
  GameType,
  BaseActionResult,
  GameAction
} from '../../../types/game';

/**
 * Configuration interface for game-specific settings
 */
export interface GameConfig {
  maxLives: number;
  gameType: GameType;
  [key: string]: unknown;
}

/**
 * Base interface for game-specific data
 */
export interface BaseGameData {
  [key: string]: unknown;
}

/**
 * Base interface for game-specific action data
 */
export interface BaseGameActionData extends GameActionData {
  action: {
    type: string;
    data: Record<string, unknown>;
  };
}

/**
 * Interface for game initialization options
 */
export interface GameInitOptions {
  roomId: string;
  socket: Socket;
  config?: Record<string, unknown>;
}

/**
 * Interface for game start options
 */
export interface GameStartOptions {
  roomId: string;
  socket: Socket;
  initialData?: Record<string, unknown>;
}

/**
 * Abstract base class for all game controllers
 * Provides common functionality while allowing game-specific customization
 */
export abstract class BaseGameController<
  TGameData extends BaseGameData = BaseGameData,
  TConfig extends GameConfig = GameConfig,
  TActionResult extends BaseActionResult = BaseActionResult
> {
  protected gameService: GameService;
  protected gameData: Map<string, TGameData> = new Map();
  protected config: TConfig;

  constructor(gameService: GameService, config: TConfig) {
    this.gameService = gameService;
    this.config = config;
  }

  /**
   * Abstract method to create initial game data
   * Must be implemented by each game controller
   */
  protected abstract createInitialGameData(roomId: string): TGameData;

  /**
   * Abstract method to handle game-specific initialization logic
   * Called after basic game state is created
   */
  protected abstract onGameInitialized(roomId: string, gameData: TGameData): void;

  /**
   * Abstract method to handle game-specific start logic
   * Called after game state is set to active
   */
  protected abstract onGameStarted(roomId: string, gameData: TGameData): void;

  /**
   * Abstract method to handle game-specific cleanup
   * Called when game ends or is cleaned up
   */
  protected abstract onGameCleanup(roomId: string): void;

  /**
   * Abstract method to handle game-specific actions
   * Must be implemented by each game controller
   */
  protected abstract handleGameSpecificAction(
    socket: Socket,
    data: BaseGameActionData
  ): Promise<TActionResult> | TActionResult;

  /**
   * Generic game initialization method
   * Handles common initialization logic and delegates to game-specific methods
   */
  public initializeGame(roomId: string, socket: Socket): GameInitResult {
    try {
      // Check if room already has a game state
      let gameState = this.gameService.getGameState(roomId);

      if (gameState) {
        // Game state exists, check if we can initialize
        if (gameState.status === 'active') {
          return { success: false, message: 'Game is already active' };
        }
        if (gameState.status === 'ended') {
          return { success: false, message: 'Game session has ended - no restarts allowed' };
        }
      } else {
        // Create new game state
        gameState = this.gameService.createGameState(
          roomId,
          this.config.gameType,
          this.config.maxLives
        );
      }

      // Create and initialize game-specific data
      const gameData = this.createInitialGameData(roomId);
      this.gameData.set(roomId, gameData);

      // Call game-specific initialization
      this.onGameInitialized(roomId, gameData);

      logger.info(`${this.config.gameType} game initialized for room ${roomId}`);

      return { success: true, gameState };
    } catch (error) {
      logger.error(`Error initializing ${this.config.gameType} game in room ${roomId}:`, error);
      return { success: false, message: 'Internal server error during initialization' };
    }
  }

  /**
   * Generic game start method
   * Handles common start logic and delegates to game-specific methods
   */
  public startGame(roomId: string, socket: Socket): GameInitResult {
    try {
      const gameState = this.gameService.getGameState(roomId);
      const gameData = this.gameData.get(roomId);

      if (!gameState) {
        return { success: false, message: 'Game not initialized. Call initializeGame first.' };
      }

      if (!gameData) {
        return { success: false, message: 'Game data not found. Call initializeGame first.' };
      }

      if (gameState.status === 'active') {
        return { success: false, message: 'Game is already active' };
      }

      if (gameState.status === 'ended') {
        return { success: false, message: 'Game session has ended - no restarts allowed' };
      }

      // Start the game using GameService
      const startSuccess = this.gameService.startGame(roomId, socket);
      if (!startSuccess) {
        return { success: false, message: 'Failed to start game' };
      }

      // Call game-specific start logic
      this.onGameStarted(roomId, gameData);

      logger.info(`${this.config.gameType} game started for room ${roomId}`);

      return { success: true, gameState };
    } catch (error) {
      logger.error(`Error starting ${this.config.gameType} game in room ${roomId}:`, error);
      return { success: false, message: 'Internal server error during game start' };
    }
  }

  /**
   * Generic game end method
   * Handles common end logic and delegates to game-specific cleanup
   */
  public endGame(roomId: string, reason: EndReason = 'manual'): boolean {
    try {
      const gameState = this.gameService.getGameState(roomId);
      if (!gameState || gameState.status !== 'active') {
        logger.warn(`Cannot end game: no active game in room ${roomId}`);
        return false;
      }

      // End game using GameService
      this.gameService.endGame(roomId, reason);

      // Call game-specific cleanup
      this.onGameCleanup(roomId);

      // Clean up game state
      this.cleanupGame(roomId);

      logger.info(`${this.config.gameType} game ended in room ${roomId}, reason: ${reason}, final score: ${gameState.score}`);
      return true;
    } catch (error) {
      logger.error(`Error ending ${this.config.gameType} game in room ${roomId}:`, error);
      return false;
    }
  }

  /**
   * Generic cleanup method
   * Removes game data and state
   */
  public cleanupGame(roomId: string): boolean {
    try {
      const deleted = this.gameService.deleteGameState(roomId);
      this.gameData.delete(roomId);

      if (deleted) {
        logger.info(`Cleaned up ${this.config.gameType} game for room ${roomId}`);
      }
      return deleted;
    } catch (error) {
      logger.error(`Error cleaning up ${this.config.gameType} game in room ${roomId}:`, error);
      return false;
    }
  }

  /**
   * Public cleanup method for external cleanup (e.g., on socket disconnect)
   */
  public cleanup(roomId: string): void {
    const gameState = this.gameService.getGameState(roomId);
    if (gameState && gameState.status === 'active') {
      // End the game due to disconnection
      this.endGame(roomId, 'manual');
    } else {
      // Just clean up data if game wasn't active
      this.cleanupGame(roomId);
    }
  }

  /**
   * Get game data for a room
   */
  protected getGameData(roomId: string): TGameData | undefined {
    return this.gameData.get(roomId);
  }

  /**
   * Emit a fatal error that stops the game instance and shows modal
   */
  protected emitFatalError(socket: Socket, roomId: string, message: string, errorType: string): void {
    // End the game if it's active
    const gameState = this.gameService.getGameState(roomId);
    if (gameState && gameState.status === 'active') {
      this.endGame(roomId, 'manual');
    }

    // Emit fatal error event
    socket.emit('game_fatal_error', {
      message,
      errorType,
      roomId,
      timestamp: Date.now()
    });

    logger.error(`Fatal error in room ${roomId} (${errorType}): ${message}`);
  }

  /**
   * Generic method to handle game actions
   * Validates common data and delegates to game-specific handler
   */
  public async handleGameAction(socket: Socket, data: BaseGameActionData): Promise<void> {
    const { roomId, gameId, action } = data;

    if (!roomId || !gameId || !action) {
      socket.emit('error', {
        message: 'Missing required data for game action (roomId, gameId, action)'
      });
      return;
    }

    try {
      // Process the game action using GameService
      const gameAction = {
        type: action.type,
        data: action.data,
        timestamp: Date.now()
      };

      const success = this.gameService.processGameAction(roomId, gameAction);

      if (success) {
        // Handle game-specific action
        const result = await this.handleGameSpecificAction(socket, data);

        if (!result.success) {
          socket.emit('error', {
            message: 'Failed to process game action'
          });
        }
      } else {
        socket.emit('error', {
          message: 'Failed to process game action'
        });
      }
    } catch (error) {
      logger.error(`Error processing game action in room ${roomId}:`, error);
      this.emitFatalError(socket, roomId, 'Internal server error during game action', 'action');
    }
  }

  /**
   * Generic socket event handlers setup
   * Sets up common socket events and delegates to game-specific handlers
   */
  public setupSocketHandlers(io: Server, socket: Socket): void {
    // Game initialization event (called at client load)
    socket.on('init', (data) => {
      if (data.gameId === this.config.gameType) {
        this.handleGameInit(socket, data);
      }
    });

    // Game start event (called after countdown)
    socket.on('start', (data) => {
      if (data.gameId === this.config.gameType) {
        this.handleGameStart(socket, data);
      }
    });

    // Generic game end event
    socket.on('end', (data) => {
      if (data.gameId === this.config.gameType) {
        this.handleGameEnd(io, socket, data);
      }
    });

    // Generic game action event (for game-specific actions)
    socket.on('action', (data) => {
      if (data.gameId === this.config.gameType) {
        this.handleGameAction(socket, data);
      }
    });

    // Setup additional game-specific socket handlers
    this.setupGameSpecificSocketHandlers(io, socket);
  }

  /**
   * Abstract method for game-specific socket handlers
   * Override this to add custom socket events
   */
  protected setupGameSpecificSocketHandlers(io: Server, socket: Socket): void {
    // Default implementation does nothing
    // Override in subclasses to add game-specific handlers
  }

  /**
   * Handle game initialization event (called at client load)
   */
  protected handleGameInit(socket: Socket, data: GameStartData): void {
    const { roomId, gameId, submitScoreId } = data;

    if (!roomId || !gameId) {
      this.emitFatalError(socket, roomId, 'Missing roomId, or gameId', 'initialization');
      return;
    }

    // Store submitScoreId in game session for later use
    if (submitScoreId) {
      console.log(`Game session ${roomId} - submitScoreId: ${submitScoreId}`);
      // TODO: Store submitScoreId in game session data for score submission
    }

    try {
      // Initialize the game (but don't start it yet)
      const result = this.initializeGame(roomId, socket);

      if (result.success && result.gameState) {
        // Get initial game state data
        const gameStateData = this.getInitialGameStateData(roomId);

        socket.emit('initialized', {
          gameState: {
            score: result.gameState.score,
            lives: result.gameState.lives,
            isActive: result.gameState.status === 'active',
            startTime: result.gameState.startTime
          },
          ...gameStateData,
          message: 'Game initialized!'
        });

        logger.info(`${gameId} game initialized in room ${roomId}`);
      } else {
        this.emitFatalError(socket, roomId, result.message || 'Failed to initialize game', 'initialization');
      }
    } catch (error) {
      logger.error(`Error initializing ${gameId} game in room ${roomId}:`, error);
      this.emitFatalError(socket, roomId, 'Internal server error during initialization', 'initialization');
    }
  }

  /**
   * Handle game start event (called after countdown)
   */
  protected handleGameStart(socket: Socket, data: GameStartData): void {
    const { roomId, gameId } = data;

    if (!roomId || !gameId) {
      this.emitFatalError(socket, roomId, 'Missing roomId, or gameId', 'start');
      return;
    }

    try {
      // Start the game (game should already be initialized)
      const result = this.startGame(roomId, socket);

      if (result.success && result.gameState) {
        // Get game state data after start
        const gameStateData = this.getGameStateData(roomId);

        socket.emit('started', {
          gameState: {
            score: result.gameState.score,
            lives: result.gameState.lives,
            isActive: result.gameState.status === 'active',
            startTime: result.gameState.startTime
          },
          ...gameStateData,
          message: 'Game started!'
        });

        logger.info(`${gameId} game started in room ${roomId}`);
      } else {
        this.emitFatalError(socket, roomId, result.message || 'Failed to start game', 'start');
      }
    } catch (error) {
      logger.error(`Error starting ${gameId} game in room ${roomId}:`, error);
      this.emitFatalError(socket, roomId, 'Internal server error during game start', 'start');
    }
  }

  /**
   * Handle generic game end event
   */
  protected handleGameEnd(io: Server, socket: Socket, data: GameEndData): void {
    const { roomId, reason = 'manual', gameId } = data;

    if (!roomId || !gameId) {
      socket.emit('error', {
        message: 'Missing roomId, or gameId'
      });
      return;
    }

    try {
      const gameState = this.gameService.getGameState(roomId);
      if (!gameState) {
        socket.emit('error', {
          message: 'Game state not found'
        });
        return;
      }

      const success = this.endGame(roomId, reason);

      if (success) {
        // Broadcast game end to all players in room
        io.to(roomId).emit('ended', {
          reason,
          finalScore: gameState.score,
        });

        logger.info(`${gameId} game ended in room ${roomId}, reason: ${reason}`);
      } else {
        socket.emit('error', {
          message: 'Failed to end game'
        });
      }
    } catch (error) {
      logger.error(`Error ending ${gameId} game in room ${roomId}:`, error);
      socket.emit('error', {
        message: 'Internal server error'
      });
    }
  }

  /**
   * Abstract method to get initial game state data for client
   * Override this to provide game-specific initial data
   */
  protected abstract getInitialGameStateData(roomId: string): Record<string, unknown>;

  /**
   * Abstract method to get current game state data for client
   * Override this to provide game-specific current data
   */
  protected abstract getGameStateData(roomId: string): Record<string, unknown>;
}
